
import './App.css'
import { useState } from 'react'
import { SpButton, SpTextInput, SpEmailInput } from "@senseproof/shared-ui";

function App() {
  const [textValue, setTextValue] = useState('');
  const [emailValue, setEmailValue] = useState('');
  const [textWithError, setTextWithError] = useState('');
  const [emailWithError, setEmailWithError] = useState('');

  return (
    <>
      <div className="p-8 bg-gray-50 min-h-screen">
        <h1 className="text-3xl font-bold mb-8 text-gray-800">🎉 Senseproof Shared UI Components</h1>

        {/* SpButton Section */}
        <section className="mb-12 bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-2xl font-semibold mb-6 text-gray-700">SpButton Components</h2>

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3 text-gray-600">Primary Buttons (Different Sizes)</h3>
            <div className="flex gap-4 flex-wrap">
              <SpButton variant="primary" size="large">Large Primary</SpButton>
              <SpButton variant="primary" size="regular">Regular Primary</SpButton>
              <SpButton variant="primary" size="small">Small Primary</SpButton>
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3 text-gray-600">Secondary Buttons (Different Sizes)</h3>
            <div className="flex gap-4 flex-wrap">
              <SpButton variant="secondary" size="large">Large Secondary</SpButton>
              <SpButton variant="secondary" size="regular">Regular Secondary</SpButton>
              <SpButton variant="secondary" size="small">Small Secondary</SpButton>
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-3 text-gray-600">Disabled Buttons</h3>
            <div className="flex gap-4 flex-wrap">
              <SpButton variant="primary" disabled={true} size="large">Disabled Primary</SpButton>
              <SpButton variant="secondary" disabled={true} size="regular">Disabled Secondary</SpButton>
            </div>
          </div>
        </section>

        {/* SpTextInput Section */}
        <section className="mb-12 bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-2xl font-semibold mb-6 text-gray-700">SpTextInput Components</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-600">Basic Text Input</h3>
              <SpTextInput
                label="Username"
                placeholder="Enter your username"
                value={textValue}
                onChange={setTextValue}
                helperText="Choose a unique username"
                variant="primary"
                size="large"
              />
            </div>

            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-600">Secondary Variant</h3>
              <SpTextInput
                label="Description"
                placeholder="Enter description"
                variant="secondary"
                size="regular"
                helperText="Optional field"
              />
            </div>

            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-600">Small Size</h3>
              <SpTextInput
                label="Tag"
                placeholder="Enter tag"
                size="small"
                variant="primary"
              />
            </div>

            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-600">With Error</h3>
              <SpTextInput
                label="Required Field"
                placeholder="This field is required"
                value={textWithError}
                onChange={setTextWithError}
                error={textWithError === '' ? 'This field is required' : ''}
                variant="primary"
                size="large"
              />
            </div>

            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-600">Disabled Input</h3>
              <SpTextInput
                label="Disabled Field"
                value="Cannot edit this"
                disabled={true}
                variant="primary"
                size="large"
              />
            </div>
          </div>
        </section>

        {/* SpEmailInput Section */}
        <section className="mb-12 bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-2xl font-semibold mb-6 text-gray-700">SpEmailInput Components</h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-600">Basic Email Input</h3>
              <SpEmailInput
                label="Email Address"
                value={emailValue}
                onChange={setEmailValue}
                helperText="We'll never share your email with anyone else"
                variant="primary"
                size="large"
                validateOn="blur"
              />
            </div>

            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-600">Secondary Variant</h3>
              <SpEmailInput
                label="Contact Email"
                placeholder="<EMAIL>"
                variant="secondary"
                size="regular"
                helperText="Optional contact information"
                validateOn="blur"
              />
            </div>

            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-600">Small Size</h3>
              <SpEmailInput
                label="Email"
                size="small"
                variant="primary"
                validateOn="blur"
              />
            </div>

            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-600">Validate on Change</h3>
              <SpEmailInput
                label="Email Address"
                value={emailWithError}
                onChange={setEmailWithError}
                helperText="Validation happens as you type"
                variant="primary"
                size="large"
                validateOn="change"
              />
            </div>

            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-600">Custom Validation</h3>
              <SpEmailInput
                label="Work Email"
                placeholder="<EMAIL>"
                helperText="Only company emails are allowed"
                customValidation={(email: string) => {
                  if (!email) return 'Email is required';
                  if (!email.includes('@company.com')) {
                    return 'Please use your company email address';
                  }
                  return null;
                }}
                variant="primary"
                size="large"
                validateOn="blur"
              />
            </div>

            <div>
              <h3 className="text-lg font-medium mb-3 text-gray-600">Disabled Email Input</h3>
              <SpEmailInput
                label="Disabled Email"
                value="<EMAIL>"
                disabled={true}
                variant="primary"
                size="large"
              />
            </div>
          </div>
        </section>

        {/* Interactive Demo Section */}
        <section className="mb-12 bg-white p-6 rounded-lg shadow-sm">
          <h2 className="text-2xl font-semibold mb-6 text-gray-700">Interactive Demo Form</h2>

          <div className="max-w-md mx-auto space-y-6">
            <SpTextInput
              label="Full Name"
              placeholder="Enter your full name"
              variant="primary"
              size="large"
              helperText="First and last name"
            />

            <SpEmailInput
              label="Email Address"
              placeholder="Enter your email"
              variant="primary"
              size="large"
              helperText="We'll send you a confirmation email"
              validateOn="blur"
            />

            <div className="flex gap-4 justify-center pt-4">
              <SpButton variant="primary" size="large">
                Submit Form
              </SpButton>
              <SpButton variant="secondary" size="large">
                Cancel
              </SpButton>
            </div>
          </div>
        </section>

        {/* Success Message */}
        <div className="text-center bg-green-50 border border-green-200 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-green-800 mb-2">✅ All Components Working!</h2>
          <p className="text-green-700">
            SpButton, SpTextInput, and SpEmailInput are successfully imported and rendered.
          </p>
        </div>
      </div>
    </>
  )
}

export default App
