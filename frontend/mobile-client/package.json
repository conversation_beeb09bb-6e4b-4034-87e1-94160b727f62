{"name": "mobile-client", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"expo": "~53.0.10", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-web": "^0.20.0"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/webpack-config": "^19.0.1", "@types/react": "~19.0.10", "css-loader": "^7.1.2", "postcss-loader": "^8.1.1", "style-loader": "^4.0.0", "typescript": "~5.8.3"}, "private": true}