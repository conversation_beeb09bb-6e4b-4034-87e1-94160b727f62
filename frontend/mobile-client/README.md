# Mobile Client

Мобильное приложение с поддержкой веб-платформы, использующее общие UI компоненты из shared-ui.

## Особенности

- **React Native for Web**: Приложение работает как на мобильных устройствах, так и в веб-браузере
- **Общие UI компоненты**: Использует адаптеры для компонентов из shared-ui (SpButton, SpTextInput, SpEmailInput)
- **Кроссплатформенность**: Единый код для iOS, Android и Web
- **Тестирование**: Полное покрытие тестами с использованием Jest и React Native Testing Library

## Архитектура

### Адаптеры компонентов

Проект использует адаптеры (`SharedUIAdapters.tsx`) для обеспечения совместимости компонентов shared-ui с React Native:

- **SpButtonAdapter**: Адаптер для кнопок с поддержкой разных вариантов и размеров
- **SpTextInputAdapter**: Адаптер для текстовых полей с валидацией и состояниями ошибок
- **SpEmailInputAdapter**: Адаптер для email полей с встроенной валидацией

### Платформо-специфичные стили

Адаптеры автоматически определяют платформу и применяют соответствующие стили:
- **Web**: Использует инлайн CSS стили для максимальной совместимости
- **Mobile**: Использует React Native StyleSheet для оптимальной производительности

## Запуск проекта

### Веб-версия
```bash
npm run web
```
Откроется в браузере по адресу http://localhost:19006

### iOS
```bash
npm run ios
```

### Android
```bash
npm run android
```

### Разработка
```bash
npm start
```
Запускает Metro bundler с возможностью выбора платформы

## Тестирование

```bash
npm test
```

Запускает Jest тесты для всех адаптеров компонентов.

## Структура проекта

```
src/
├── components/
│   ├── SharedUIAdapters.tsx    # Адаптеры для shared-ui компонентов
│   ├── SharedUIDemo.tsx        # Демо-страница с примерами использования
│   └── __tests__/
│       └── SharedUIAdapters.test.tsx  # Тесты для адаптеров
```

## Использование компонентов

```tsx
import { SpButtonAdapter as SpButton, SpTextInputAdapter as SpTextInput } from './SharedUIAdapters';

function MyComponent() {
  return (
    <View>
      <SpButton variant="primary" size="large" onClick={() => console.log('Clicked!')}>
        Нажми меня
      </SpButton>
      
      <SpTextInput
        label="Имя пользователя"
        placeholder="Введите имя"
        variant="primary"
        size="large"
      />
    </View>
  );
}
```

## Преимущества подхода

1. **Легкость поддержки**: Один код для всех платформ
2. **Консистентность**: Единый дизайн между web-admin и mobile-client
3. **Производительность**: Оптимизированные стили для каждой платформы
4. **Тестируемость**: Полное покрытие тестами
5. **Масштабируемость**: Легко добавлять новые компоненты

## Технологии

- React Native
- Expo
- React Native for Web
- TypeScript
- Jest
- React Native Testing Library
