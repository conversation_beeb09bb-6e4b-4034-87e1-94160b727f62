# Mobile Client

Mobile application with web platform support, using shared UI components from shared-ui.

## Features

- **React Native for Web**: Application works on both mobile devices and web browsers
- **Shared UI Components**: Uses adapters for components from shared-ui (SpButton, SpTextInput, SpEmailInput)
- **Cross-platform**: Single codebase for iOS, Android and Web
- **Testing**: Full test coverage using Jest and React Native Testing Library

## Architecture

### Component Adapters

The project uses adapters (`SharedUIAdapters.tsx`) to ensure compatibility of shared-ui components with React Native:

- **SpButtonAdapter**: Adapter for buttons with support for different variants and sizes
- **SpTextInputAdapter**: Adapter for text fields with validation and error states
- **SpEmailInputAdapter**: Adapter for email fields with built-in validation

### Platform-specific Styles

Adapters automatically detect the platform and apply appropriate styles:
- **Web**: Uses inline CSS styles for maximum compatibility
- **Mobile**: Uses React Native StyleSheet for optimal performance

## Running the Project

### Web Version
```bash
npm run web
```
Opens in browser at http://localhost:19006

### iOS
```bash
npm run ios
```

### Android
```bash
npm run android
```

### Development
```bash
npm start
```
Starts Metro bundler with platform selection options

## Testing

```bash
npm test
```

Runs Jest tests for all component adapters.

## Project Structure

```
src/
├── components/
│   ├── SharedUIAdapters.tsx    # Adapters for shared-ui components
│   ├── SharedUIDemo.tsx        # Demo page with usage examples
│   └── __tests__/
│       └── SharedUIAdapters.test.tsx  # Tests for adapters
```

## Using Components

```tsx
import { SpButtonAdapter as SpButton, SpTextInputAdapter as SpTextInput } from './SharedUIAdapters';

function MyComponent() {
  return (
    <View>
      <SpButton variant="primary" size="large" onClick={() => console.log('Clicked!')}>
        Click Me
      </SpButton>

      <SpTextInput
        label="Username"
        placeholder="Enter username"
        variant="primary"
        size="large"
      />
    </View>
  );
}
```

## Approach Benefits

1. **Easy Maintenance**: Single codebase for all platforms
2. **Consistency**: Unified design between web-admin and mobile-client
3. **Performance**: Optimized styles for each platform
4. **Testability**: Full test coverage
5. **Scalability**: Easy to add new components

## Technologies

- React Native
- Expo
- React Native for Web
- TypeScript
- Jest
- React Native Testing Library
