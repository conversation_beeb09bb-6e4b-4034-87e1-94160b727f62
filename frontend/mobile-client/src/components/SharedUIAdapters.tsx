import React from 'react';
import { Platform, Text, TouchableOpacity, TextInput, View, StyleSheet, ViewStyle, TextStyle } from 'react-native';

// Types for our adapters
interface ButtonProps {
  children: React.ReactNode;
  onClick?: () => void;
  disabled?: boolean;
  variant?: 'primary' | 'secondary';
  size?: 'large' | 'regular' | 'small';
}

interface TextInputProps {
  value?: string;
  onChange?: (value: string) => void;
  placeholder?: string;
  disabled?: boolean;
  variant?: 'primary' | 'secondary';
  size?: 'large' | 'regular' | 'small';
  label?: string;
  error?: string;
  helperText?: string;
}

interface EmailInputProps extends TextInputProps {
  customValidation?: (email: string) => string | null;
  validateOn?: 'blur' | 'change';
}

// Adapter for SpButton
export const SpButtonAdapter: React.FC<ButtonProps> = ({
  children,
  onClick,
  disabled = false,
  variant = 'primary',
  size = 'large',
}) => {
  if (Platform.OS === 'web') {
    // On web, use regular button with inline styles
    const buttonStyle = {
      borderRadius: 6,
      fontWeight: '600',
      transition: 'all 0.2s',
      border: 'none',
      cursor: disabled ? 'not-allowed' : 'pointer',
      ...((size || 'large') === 'large' && { padding: '8px 16px', fontSize: '16px' }),
      ...((size || 'large') === 'regular' && { padding: '6px 12px', fontSize: '14px' }),
      ...((size || 'large') === 'small' && { padding: '4px 8px', fontSize: '12px' }),
      ...((variant || 'primary') === 'primary' && !disabled && {
        backgroundColor: '#1a1a1a',
        color: 'white',
      }),
      ...((variant || 'primary') === 'secondary' && !disabled && {
        backgroundColor: 'white',
        color: '#1a1a1a',
        border: '1px solid #d1d5db',
      }),
      ...(disabled && {
        backgroundColor: '#f7f7f7',
        color: '#a3a3a3',
        border: '1px solid #e5e7eb',
      }),
    };

    return (
      <button
        style={buttonStyle}
        onClick={onClick}
        disabled={disabled}
      >
        {children}
      </button>
    );
  }

  // On mobile platforms, use TouchableOpacity
  const getButtonStyle = (): ViewStyle[] => {
    const baseStyle: ViewStyle[] = [styles.button];

    if ((size || 'large') === 'large') baseStyle.push(styles.buttonLarge);
    if ((size || 'large') === 'regular') baseStyle.push(styles.buttonRegular);
    if ((size || 'large') === 'small') baseStyle.push(styles.buttonSmall);

    if ((variant || 'primary') === 'primary' && !disabled) baseStyle.push(styles.buttonPrimary);
    if ((variant || 'primary') === 'secondary' && !disabled) baseStyle.push(styles.buttonSecondary);
    if (disabled) baseStyle.push(styles.buttonDisabled);

    return baseStyle;
  };

  const getTextStyle = (): TextStyle[] => {
    const baseStyle: TextStyle[] = [styles.buttonText];

    if ((size || 'large') === 'large') baseStyle.push(styles.buttonTextLarge);
    if ((size || 'large') === 'regular') baseStyle.push(styles.buttonTextRegular);
    if ((size || 'large') === 'small') baseStyle.push(styles.buttonTextSmall);

    if ((variant || 'primary') === 'primary' && !disabled) baseStyle.push(styles.buttonTextPrimary);
    if ((variant || 'primary') === 'secondary' && !disabled) baseStyle.push(styles.buttonTextSecondary);
    if (disabled) baseStyle.push(styles.buttonTextDisabled);

    return baseStyle;
  };

  return (
    <TouchableOpacity
      style={getButtonStyle()}
      onPress={onClick}
      disabled={disabled}
    >
      <Text style={getTextStyle()}>{children}</Text>
    </TouchableOpacity>
  );
};

// Adapter for SpTextInput
export const SpTextInputAdapter: React.FC<TextInputProps> = ({
  value,
  onChange,
  placeholder,
  disabled = false,
  variant = 'primary',
  size = 'large',
  label,
  error,
  helperText,
}) => {
  if (Platform.OS === 'web') {
    const inputStyle = {
      borderRadius: 6,
      border: '1px solid #d1d5db',
      fontWeight: '400',
      transition: 'all 0.2s',
      width: '100%',
      ...((size || 'large') === 'large' && { padding: '8px 16px', fontSize: '16px' }),
      ...((size || 'large') === 'regular' && { padding: '6px 12px', fontSize: '14px' }),
      ...((size || 'large') === 'small' && { padding: '4px 8px', fontSize: '12px' }),
      ...((variant || 'primary') === 'primary' && !disabled && !error && {
        backgroundColor: 'white',
        color: '#1a1a1a',
        borderColor: '#d1d5db',
      }),
      ...((variant || 'primary') === 'secondary' && !disabled && !error && {
        backgroundColor: 'white',
        color: '#1a1a1a',
        borderColor: '#e5e7eb',
      }),
      ...(error && !disabled && {
        backgroundColor: 'white',
        color: '#1a1a1a',
        borderColor: '#ef4444',
      }),
      ...(disabled && {
        backgroundColor: '#f7f7f7',
        color: '#a3a3a3',
        borderColor: '#e5e7eb',
        cursor: 'not-allowed',
      }),
    };

    const labelStyle = {
      display: 'block',
      fontSize: '14px',
      fontWeight: '500',
      marginBottom: '4px',
      color: error && !disabled ? '#dc2626' : disabled ? '#a3a3a3' : '#1a1a1a',
    };

    const helperStyle = {
      marginTop: '4px',
      fontSize: '12px',
      color: error ? '#dc2626' : '#5e6c76',
    };

    return (
      <div style={{ width: '100%' }}>
        {label && <label style={labelStyle}>{label}</label>}
        <input
          type="text"
          value={value}
          onChange={(e) => onChange?.(e.target.value)}
          placeholder={placeholder}
          disabled={disabled}
          style={inputStyle}
        />
        {(error || helperText) && (
          <div style={helperStyle}>
            {error || helperText}
          </div>
        )}
      </div>
    );
  }

  // React Native version
  const getInputStyle = (): TextStyle[] => {
    const baseStyle: TextStyle[] = [styles.input];

    if ((size || 'large') === 'large') baseStyle.push(styles.inputLarge);
    if ((size || 'large') === 'regular') baseStyle.push(styles.inputRegular);
    if ((size || 'large') === 'small') baseStyle.push(styles.inputSmall);

    if ((variant || 'primary') === 'primary' && !disabled && !error) baseStyle.push(styles.inputPrimary);
    if ((variant || 'primary') === 'secondary' && !disabled && !error) baseStyle.push(styles.inputSecondary);
    if (error && !disabled) baseStyle.push(styles.inputError);
    if (disabled) baseStyle.push(styles.inputDisabled);

    return baseStyle;
  };

  const getLabelStyle = (): TextStyle[] => {
    const baseStyle: TextStyle[] = [styles.label];
    if (error && !disabled) baseStyle.push(styles.labelError);
    if (disabled) baseStyle.push(styles.labelDisabled);
    return baseStyle;
  };

  const getHelperStyle = (): TextStyle[] => {
    const baseStyle: TextStyle[] = [styles.helperText];
    if (error) baseStyle.push(styles.helperTextError);
    return baseStyle;
  };

  return (
    <View style={styles.inputContainer}>
      {label && <Text style={getLabelStyle()}>{label}</Text>}
      <TextInput
        value={value}
        onChangeText={onChange}
        placeholder={placeholder}
        editable={!disabled}
        style={getInputStyle()}
      />
      {(error || helperText) && (
        <Text style={getHelperStyle()}>
          {error || helperText}
        </Text>
      )}
    </View>
  );
};

// Adapter for SpEmailInput (extends SpTextInput)
export const SpEmailInputAdapter: React.FC<EmailInputProps> = (props) => {
  // For simplicity, use the same adapter as for text field
  // In a real application, email validation would be implemented here
  return <SpTextInputAdapter {...props} />;
};

const styles = StyleSheet.create({
  // Button styles
  button: {
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
    marginVertical: 2,
  },
  buttonLarge: {
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  buttonRegular: {
    paddingHorizontal: 12,
    paddingVertical: 6,
  },
  buttonSmall: {
    paddingHorizontal: 8,
    paddingVertical: 4,
  },
  buttonPrimary: {
    backgroundColor: '#1a1a1a',
  },
  buttonSecondary: {
    backgroundColor: 'white',
    borderWidth: 1,
    borderColor: '#d1d5db',
  },
  buttonDisabled: {
    backgroundColor: '#f7f7f7',
    borderWidth: 1,
    borderColor: '#e5e7eb',
  },
  buttonText: {
    fontWeight: '600',
  },
  buttonTextLarge: {
    fontSize: 16,
  },
  buttonTextRegular: {
    fontSize: 14,
  },
  buttonTextSmall: {
    fontSize: 12,
  },
  buttonTextPrimary: {
    color: 'white',
  },
  buttonTextSecondary: {
    color: '#1a1a1a',
  },
  buttonTextDisabled: {
    color: '#a3a3a3',
  },

  // Input styles
  inputContainer: {
    width: '100%',
    marginBottom: 8,
  },
  input: {
    borderRadius: 6,
    borderWidth: 1,
    fontWeight: '400',
    width: '100%',
  },
  inputLarge: {
    paddingHorizontal: 16,
    paddingVertical: 8,
    fontSize: 16,
  },
  inputRegular: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    fontSize: 14,
  },
  inputSmall: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    fontSize: 12,
  },
  inputPrimary: {
    backgroundColor: 'white',
    color: '#1a1a1a',
    borderColor: '#d1d5db',
  },
  inputSecondary: {
    backgroundColor: 'white',
    color: '#1a1a1a',
    borderColor: '#e5e7eb',
  },
  inputError: {
    backgroundColor: 'white',
    color: '#1a1a1a',
    borderColor: '#ef4444',
  },
  inputDisabled: {
    backgroundColor: '#f7f7f7',
    color: '#a3a3a3',
    borderColor: '#e5e7eb',
  },
  label: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
    color: '#1a1a1a',
  },
  labelError: {
    color: '#dc2626',
  },
  labelDisabled: {
    color: '#a3a3a3',
  },
  helperText: {
    marginTop: 4,
    fontSize: 12,
    color: '#5e6c76',
  },
  helperTextError: {
    color: '#dc2626',
  },
});
