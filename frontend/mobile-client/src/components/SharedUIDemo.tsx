import React, { useState } from 'react';
import { ScrollView, View, Text, StyleSheet, Platform } from 'react-native';
import { SpButtonAdapter as Sp<PERSON>utton, SpTextInputAdapter as SpTextInput, SpEmailInputAdapter as SpEmailInput } from './SharedUIAdapters';

// Adapter for web styles in React Native
const WebStyleAdapter: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  if (Platform.OS === 'web') {
    // On web platform, use div with CSS classes
    return (
      <div style={{ 
        padding: 32, 
        backgroundColor: '#f9fafb', 
        minHeight: '100vh',
        fontFamily: 'system-ui, -apple-system, sans-serif'
      }}>
        {children}
      </div>
    );
  }

  // On mobile platforms, use React Native styles
  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      {children}
    </ScrollView>
  );
};

const SectionContainer: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => {
  if (Platform.OS === 'web') {
    return (
      <section style={{
        marginBottom: 48,
        backgroundColor: 'white',
        padding: 24,
        borderRadius: 8,
        boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1)'
      }}>
        <h2 style={{
          fontSize: 24,
          fontWeight: '600',
          marginBottom: 24,
          color: '#374151'
        }}>
          {title}
        </h2>
        {children}
      </section>
    );
  }
  
  return (
    <View style={styles.section}>
      <Text style={styles.sectionTitle}>{title}</Text>
      {children}
    </View>
  );
};

const SubsectionContainer: React.FC<{ title: string; children: React.ReactNode }> = ({ title, children }) => {
  if (Platform.OS === 'web') {
    return (
      <div style={{ marginBottom: 24 }}>
        <h3 style={{
          fontSize: 18,
          fontWeight: '500',
          marginBottom: 12,
          color: '#4b5563'
        }}>
          {title}
        </h3>
        <div style={{ display: 'flex', gap: 16, flexWrap: 'wrap' }}>
          {children}
        </div>
      </div>
    );
  }
  
  return (
    <View style={styles.subsection}>
      <Text style={styles.subsectionTitle}>{title}</Text>
      <View style={styles.componentRow}>
        {children}
      </View>
    </View>
  );
};

export const SharedUIDemo: React.FC = () => {
  const [textValue, setTextValue] = useState('');
  const [emailValue, setEmailValue] = useState('');
  const [textWithError, setTextWithError] = useState('');
  const [emailWithError, setEmailWithError] = useState('');

  const MainTitle = Platform.OS === 'web' ? 
    () => (
      <h1 style={{
        fontSize: 30,
        fontWeight: 'bold',
        marginBottom: 32,
        color: '#1f2937'
      }}>
        🎉 Senseproof Shared UI Components
      </h1>
    ) :
    () => <Text style={styles.mainTitle}>🎉 Senseproof Shared UI Components</Text>;

  return (
    <WebStyleAdapter>
      <MainTitle />

      {/* SpButton Section */}
      <SectionContainer title="SpButton Components">
        <SubsectionContainer title="Primary Buttons (Different Sizes)">
          <SpButton variant="primary" size="large">Large Primary</SpButton>
          <SpButton variant="primary" size="regular">Regular Primary</SpButton>
          <SpButton variant="primary" size="small">Small Primary</SpButton>
        </SubsectionContainer>

        <SubsectionContainer title="Secondary Buttons (Different Sizes)">
          <SpButton variant="secondary" size="large">Large Secondary</SpButton>
          <SpButton variant="secondary" size="regular">Regular Secondary</SpButton>
          <SpButton variant="secondary" size="small">Small Secondary</SpButton>
        </SubsectionContainer>

        <SubsectionContainer title="Disabled Buttons">
          <SpButton variant="primary" disabled={true} size="large">Disabled Primary</SpButton>
          <SpButton variant="secondary" disabled={true} size="regular">Disabled Secondary</SpButton>
        </SubsectionContainer>
      </SectionContainer>

      {/* SpTextInput Section */}
      <SectionContainer title="SpTextInput Components">
        <View style={Platform.OS === 'web' ? { display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 24 } : styles.gridContainer}>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Basic Text Input</Text>
            <SpTextInput
              label="Username"
              placeholder="Enter your username"
              value={textValue}
              onChange={setTextValue}
              helperText="Choose a unique username"
              variant="primary"
              size="large"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Secondary Variant</Text>
            <SpTextInput
              label="Description"
              placeholder="Enter description"
              variant="secondary"
              size="regular"
              helperText="Optional field"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Small Size</Text>
            <SpTextInput
              label="Tag"
              placeholder="Enter tag"
              size="small"
              variant="primary"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>With Error</Text>
            <SpTextInput
              label="Required Field"
              placeholder="This field is required"
              value={textWithError}
              onChange={setTextWithError}
              error={textWithError === '' ? 'This field is required' : ''}
              variant="primary"
              size="large"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Disabled Input</Text>
            <SpTextInput
              label="Disabled Field"
              value="Cannot edit this"
              disabled={true}
              variant="primary"
              size="large"
            />
          </View>
        </View>
      </SectionContainer>

      {/* SpEmailInput Section */}
      <SectionContainer title="SpEmailInput Components">
        <View style={Platform.OS === 'web' ? { display: 'grid', gridTemplateColumns: '1fr 1fr', gap: 24 } : styles.gridContainer}>
          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Basic Email Input</Text>
            <SpEmailInput
              label="Email Address"
              value={emailValue}
              onChange={setEmailValue}
              helperText="We'll never share your email with anyone else"
              variant="primary"
              size="large"
              validateOn="blur"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Secondary Variant</Text>
            <SpEmailInput
              label="Contact Email"
              placeholder="<EMAIL>"
              variant="secondary"
              size="regular"
              helperText="Optional contact information"
              validateOn="blur"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Small Size</Text>
            <SpEmailInput
              label="Email"
              size="small"
              variant="primary"
              validateOn="blur"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Validate on Change</Text>
            <SpEmailInput
              label="Email Address"
              value={emailWithError}
              onChange={setEmailWithError}
              helperText="Validation happens as you type"
              variant="primary"
              size="large"
              validateOn="change"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Custom Validation</Text>
            <SpEmailInput
              label="Work Email"
              placeholder="<EMAIL>"
              helperText="Only company emails are allowed"
              customValidation={(email: string) => {
                if (!email) return 'Email is required';
                if (!email.includes('@company.com')) {
                  return 'Please use your company email address';
                }
                return null;
              }}
              variant="primary"
              size="large"
              validateOn="blur"
            />
          </View>

          <View style={styles.inputContainer}>
            <Text style={styles.inputLabel}>Disabled Email Input</Text>
            <SpEmailInput
              label="Disabled Email"
              value="<EMAIL>"
              disabled={true}
              variant="primary"
              size="large"
            />
          </View>
        </View>
      </SectionContainer>

      {/* Interactive Demo Section */}
      <SectionContainer title="Interactive Demo Form">
        <View style={styles.demoForm}>
          <SpTextInput
            label="Full Name"
            placeholder="Enter your full name"
            variant="primary"
            size="large"
            helperText="First and last name"
          />

          <SpEmailInput
            label="Email Address"
            placeholder="Enter your email"
            variant="primary"
            size="large"
            helperText="We'll send you a confirmation email"
            validateOn="blur"
          />

          <View style={styles.buttonRow}>
            <SpButton variant="primary" size="large">
              Submit Form
            </SpButton>
            <SpButton variant="secondary" size="large">
              Cancel
            </SpButton>
          </View>
        </View>
      </SectionContainer>

      {/* Success Message */}
      <View style={styles.successMessage}>
        <Text style={styles.successTitle}>✅ All Components Working!</Text>
        <Text style={styles.successText}>
          SpButton, SpTextInput, and SpEmailInput are successfully imported and rendered.
        </Text>
      </View>
    </WebStyleAdapter>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f9fafb',
  },
  contentContainer: {
    padding: 20,
  },
  mainTitle: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 24,
    color: '#1f2937',
    textAlign: 'center',
  },
  section: {
    marginBottom: 32,
    backgroundColor: 'white',
    padding: 16,
    borderRadius: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 2,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    marginBottom: 16,
    color: '#374151',
  },
  subsection: {
    marginBottom: 16,
  },
  subsectionTitle: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#4b5563',
  },
  componentRow: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  gridContainer: {
    gap: 16,
  },
  inputContainer: {
    marginBottom: 16,
  },
  inputLabel: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: 8,
    color: '#4b5563',
  },
  demoForm: {
    maxWidth: 400,
    alignSelf: 'center',
    gap: 16,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
    paddingTop: 16,
  },
  successMessage: {
    textAlign: 'center',
    backgroundColor: '#f0fdf4',
    borderColor: '#bbf7d0',
    borderWidth: 1,
    borderRadius: 8,
    padding: 24,
  },
  successTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#166534',
    marginBottom: 8,
  },
  successText: {
    color: '#15803d',
  },
});
